-- Migration to update Vote table schema
-- Change from composite primary key (chatId, messageId) to single primary key (messageId)
-- Remove foreign key constraint to Chat table but keep chatId for reference

-- Drop the existing composite primary key constraint
ALTER TABLE "Vote" DROP CONSTRAINT "Vote_chatId_messageId_pk";

-- Drop the foreign key constraint to Chat table
ALTER TABLE "Vote" DROP CONSTRAINT "Vote_chatId_Chat_id_fk";

-- Add messageId as the new primary key
ALTER TABLE "Vote" ADD CONSTRAINT "Vote_messageId_pk" PRIMARY KEY ("messageId");

-- Note: We keep the chatId column for reference but without foreign key constraint
-- This allows the same message to be voted on regardless of which chat context it's viewed in
